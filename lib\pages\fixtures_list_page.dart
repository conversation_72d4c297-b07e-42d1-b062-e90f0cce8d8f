import 'dart:convert';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:cat_tv/models/fixture.dart';

class FixturesListPage extends StatefulWidget {
  const FixturesListPage({super.key});

  @override
  State<FixturesListPage> createState() => _FixturesListPageState();
}

class _FixturesListPageState extends State<FixturesListPage> {
  List<FixtureLeague> _leagues = [];
  bool _isLoading = true;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _loadFixtures();
  }

  Future<void> _loadFixtures() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      final file = File('cache/fixtures_data.json');
      if (await file.exists()) {
        final jsonString = await file.readAsString();
        final List<dynamic> jsonData = json.decode(jsonString);

        setState(() {
          _leagues =
              jsonData.map((league) => FixtureLeague.fromJson(league)).toList();
          _isLoading = false;
        });
      } else {
        setState(() {
          _errorMessage = 'Fixtures data file not found at: ${file.path}';
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Error loading fixtures: $e';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              _errorMessage!,
              style: TextStyle(fontSize: 16, color: Colors.grey[600]),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadFixtures,
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    if (_leagues.isEmpty) {
      return const Center(
        child: Text('No fixtures available', style: TextStyle(fontSize: 16)),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadFixtures,
      child: ListView.builder(
        itemCount: _leagues.length,
        itemBuilder: (context, index) {
          final league = _leagues[index];
          return _buildLeagueCard(league);
        },
      ),
    );
  }

  Widget _buildLeagueCard(FixtureLeague league) {
    if (league.matches.isEmpty) {
      return const SizedBox.shrink(); // Don't show leagues with no matches
    }

    return Card(
      margin: const EdgeInsets.all(8.0),
      child: ExpansionTile(
        leading: Image.network(
          league.flagUrl,
          width: 32,
          height: 24,
          errorBuilder: (context, error, stackTrace) {
            return Icon(Icons.flag, size: 24, color: Colors.grey[400]);
          },
        ),
        title: Text(
          league.leagueName,
          style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
        ),
        subtitle: Text('${league.matches.length} matches'),
        children:
            league.matches.map((match) => _buildMatchTile(match)).toList(),
      ),
    );
  }

  Widget _buildMatchTile(FixtureMatch match) {
    final parsed = match.parsedTeams;
    final status = match.status;

    return ListTile(
      contentPadding: const EdgeInsets.symmetric(
        horizontal: 16.0,
        vertical: 8.0,
      ),
      title: Row(
        children: [
          _buildTeamWithLogo(parsed['homeTeam'], true),
          const SizedBox(width: 8),
          _buildScoreWidget(parsed, status),
          const SizedBox(width: 8),
          _buildTeamWithLogo(parsed['awayTeam'], false),
        ],
      ),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: 4),
          Row(
            children: [
              _buildStatusChip(status),
              const SizedBox(width: 8),
              Text(
                match.time,
                style: TextStyle(color: Colors.grey[600], fontSize: 12),
              ),
              if (status == MatchStatus.upcoming &&
                  match.timeUntilStart != null)
                Text(
                  ' • ${_formatTimeUntilStart(match.timeUntilStart!)}',
                  style: TextStyle(color: Colors.grey[600], fontSize: 12),
                ),
            ],
          ),
          if (match.channels.isNotEmpty) ...[
            const SizedBox(height: 4),
            Wrap(
              spacing: 4,
              children:
                  match.channels.take(3).map((channel) {
                    return Chip(
                      label: Text(
                        channel,
                        style: const TextStyle(fontSize: 10),
                      ),
                      materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                      visualDensity: VisualDensity.compact,
                    );
                  }).toList(),
            ),
            if (match.channels.length > 3)
              Text(
                '+${match.channels.length - 3} more channels',
                style: TextStyle(fontSize: 10, color: Colors.grey[600]),
              ),
          ],
        ],
      ),
      onTap: () {
        _showMatchDetails(match);
      },
    );
  }

  Widget _buildTeamWithLogo(String teamName, bool isHome) {
    return Expanded(
      child: Row(
        mainAxisAlignment:
            isHome ? MainAxisAlignment.start : MainAxisAlignment.end,
        children: [
          if (isHome) ...[_buildTeamLogo(teamName), const SizedBox(width: 8)],
          Flexible(
            child: Text(
              teamName,
              style: const TextStyle(fontWeight: FontWeight.w500),
              overflow: TextOverflow.ellipsis,
              textAlign: isHome ? TextAlign.left : TextAlign.right,
            ),
          ),
          if (!isHome) ...[const SizedBox(width: 8), _buildTeamLogo(teamName)],
        ],
      ),
    );
  }

  Widget _buildTeamLogo(String teamName) {
    // Generate a simple team logo URL based on team name
    // This is a simplified approach - in a real app you'd have a proper team database
    final logoUrl = _getTeamLogoUrl(teamName);

    return Container(
      width: 24,
      height: 24,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        border: Border.all(color: Colors.grey[300]!, width: 1),
      ),
      child: ClipOval(
        child: Image.network(
          logoUrl,
          width: 24,
          height: 24,
          fit: BoxFit.cover,
          errorBuilder: (context, error, stackTrace) {
            return Container(
              color: Colors.grey[200],
              child: Icon(
                Icons.sports_soccer,
                size: 16,
                color: Colors.grey[400],
              ),
            );
          },
        ),
      ),
    );
  }

  String _getTeamLogoUrl(String teamName) {
    // This is a simplified approach to get team logos
    // In a real implementation, you'd have a proper mapping of team names to logo URLs
    final cleanName = teamName
        .toLowerCase()
        .replaceAll(' ', '-')
        .replaceAll('&', 'and')
        .replaceAll('.', '')
        .replaceAll('ç', 'c')
        .replaceAll('ş', 's')
        .replaceAll('ğ', 'g')
        .replaceAll('ı', 'i')
        .replaceAll('ö', 'o')
        .replaceAll('ü', 'u');

    return 'https://www.livesoccertv.com/images/teams/$cleanName.png';
  }

  Widget _buildScoreWidget(Map<String, dynamic> parsed, MatchStatus status) {
    if (parsed['hasScore']) {
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
        decoration: BoxDecoration(
          color: status == MatchStatus.live ? Colors.red : Colors.grey[300],
          borderRadius: BorderRadius.circular(4),
        ),
        child: Text(
          '${parsed['homeScore']} - ${parsed['awayScore']}',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: status == MatchStatus.live ? Colors.white : Colors.black87,
          ),
        ),
      );
    } else {
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
        decoration: BoxDecoration(
          color: Colors.grey[200],
          borderRadius: BorderRadius.circular(4),
        ),
        child: const Text(
          'vs',
          style: TextStyle(fontWeight: FontWeight.bold, color: Colors.black54),
        ),
      );
    }
  }

  Widget _buildStatusChip(MatchStatus status) {
    Color color;
    String text;

    switch (status) {
      case MatchStatus.live:
        color = Colors.red;
        text = 'LIVE';
        break;
      case MatchStatus.finished:
        color = Colors.grey;
        text = 'FT';
        break;
      case MatchStatus.upcoming:
        color = Colors.blue;
        text = 'UPCOMING';
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(3),
      ),
      child: Text(
        text,
        style: const TextStyle(
          color: Colors.white,
          fontSize: 10,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  String _formatTimeUntilStart(Duration duration) {
    if (duration.inDays > 0) {
      return '${duration.inDays}d ${duration.inHours % 24}h';
    } else if (duration.inHours > 0) {
      return '${duration.inHours}h ${duration.inMinutes % 60}m';
    } else {
      return '${duration.inMinutes}m';
    }
  }

  void _showMatchDetails(FixtureMatch match) {
    showModalBottomSheet(
      context: context,
      builder: (context) {
        return Container(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                match.title,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Text('Time: ${match.time}'),
              const SizedBox(height: 8),
              const Text(
                'Broadcasting Channels:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 4),
              ...match.channels.map(
                (channel) => Padding(
                  padding: const EdgeInsets.only(left: 8, top: 2),
                  child: Text('• $channel'),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
