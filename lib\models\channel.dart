class Channel {
  final String channelId;
  final String name;
  final List<String> altNames;
  final String countryCode;
  final int? categoryId;
  final String? logoUrl;
  final bool isActive;
  final bool isExternal;

  Channel({
    required this.channelId,
    required this.name,
    required this.altNames,
    required this.countryCode,
    this.categoryId,
    this.logoUrl,
    required this.isActive,
    required this.isExternal,
  });

  factory Channel.fromMap(Map<String, dynamic> map) {
    return Channel(
      channelId: map['channel_id'],
      name: map['name'],
      altNames: (map['alt_names'] as String?)?.split(',') ?? [],
      countryCode: map['country_code'],
      categoryId: map['category_id'],
      logoUrl: map['logo_url'],
      isActive: map['is_active'] == 1,
      isExternal: map['is_external'] == 1,
    );
  }

  String get channelNameOrDefault {
    return name.isNotEmpty ? name : 'Unknown Channel';
  }

  String get altNamesOrDefault {
    return altNames.isNotEmpty ? altNames.join(', ') : 'No alternative names';
  }

  String get logoUrlOrDefault {
    return logoUrl ?? 'https://static.thenounproject.com/png/3548799-200.png';
  }

  String get categoryIdOrDefault {
    return categoryId?.toString() ?? 'No category';
  }

  String get countryCodeOrDefault {
    return countryCode.isNotEmpty ? countryCode : 'No country code';
  }

  get isActiveOrDefault {
    return isActive ? 'Active' : 'Inactive';
  }
}
