import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:cat_tv/models/channel.dart';

class WebViewPage extends StatefulWidget {
  final Channel channel;
  final String channelUrl;
  const WebViewPage({
    super.key,
    required this.channel,
    required this.channelUrl,
  });

  @override
  State<WebViewPage> createState() => _WebViewPageState();
}

class _WebViewPageState extends State<WebViewPage> {
  InAppWebViewController? _webViewController;
  late final TextEditingController _urlController;
  bool _shouldBlockAds = true;
  bool _isDisposing =
      false; // New flag to control widget rendering during disposal

  // Enhanced ad blocking patterns including aggressive redirects and downloads
  final List<String> _adBlockPatterns = [
    // Common ad networks
    'doubleclick.net',
    'googleadservices.com',
    'googlesyndication.com',
    'googletagmanager.com',
    'google-analytics.com',
    'facebook.com/tr',
    'amazon-adsystem.com',
    'adsystem.amazon.com',
    'ads.yahoo.com',
    'advertising.com',
    'adsystem.com',
    'adsense.com',
    'adnxs.com',
    'ads.twitter.com',
    'analytics.twitter.com',
    'ads.linkedin.com',
    'ads.pinterest.com',
    'ads.reddit.com',
    'outbrain.com',
    'taboola.com',
    'scorecardresearch.com',
    'quantserve.com',

    // Aggressive redirect and download patterns
    'operagx.gg',
    'opera.com',
    'operagx.com',
    'download.opera.com',
    'get.opera.com',
    'install.opera.com',
    'setup.opera.com',
    'redirect.opera.com',
    'cdn.opera.com',
    'operacdn.com',
    'operasoftware.com',
    'opera-api.com',

    // Common redirect domains
    'bit.ly',
    'tinyurl.com',
    'short.link',
    'redirect.link',
    'go.link',
    'click.link',
    'track.link',
    'affiliate.link',
    'promo.link',
    'offer.link',

    // Download and installer patterns
    'download.',
    'installer.',
    'setup.',
    'install.',
    'get.',
    'fetch.',
    'grab.',
    'dl.',
    '.exe',
    '.msi',
    '.dmg',
    '.pkg',
    '.deb',
    '.rpm',

    // Generic ad patterns
    'adsystem',
    'advertising',
    'googleads',
    'adsense',
    'adservice',
    'adserver',
    'adnetwork',
    'adnxs',
    'ads.',
    '/ads/',
    'advertisement',
    'popup',
    'popunder',
    'interstitial',
    'overlay',
    'banner',
    'sponsored',
    'promo',
    'affiliate',
    'referral',
    'tracking',
    'analytics',
    'metrics',
    'telemetry',
  ];

  bool _shouldBlockUrl(String url) {
    if (!_shouldBlockAds) return false;

    final lowerUrl = url.toLowerCase();

    // Check against our pattern list
    if (_adBlockPatterns.any((pattern) => lowerUrl.contains(pattern))) {
      return true;
    }

    // Additional checks for aggressive redirects
    if (_isAggressiveRedirect(lowerUrl)) {
      return true;
    }

    // Block download attempts
    if (_isDownloadAttempt(lowerUrl)) {
      return true;
    }

    return false;
  }

  bool _isAggressiveRedirect(String url) {
    // Block URLs that look like aggressive redirects
    final redirectPatterns = [
      RegExp(r'redirect\..*'),
      RegExp(r'.*\/redirect\/.*'),
      RegExp(r'.*\/go\/.*'),
      RegExp(r'.*\/click\/.*'),
      RegExp(r'.*\/track\/.*'),
      RegExp(r'.*\/out\/.*'),
      RegExp(r'.*\/exit\/.*'),
      RegExp(r'.*\/link\/.*'),
      RegExp(r'.*\/promo\/.*'),
      RegExp(r'.*\/offer\/.*'),
      RegExp(r'.*\/download\/.*'),
      RegExp(r'.*\/install\/.*'),
      RegExp(r'.*\/setup\/.*'),
      RegExp(r'.*\/get\/.*'),
    ];

    return redirectPatterns.any((pattern) => pattern.hasMatch(url));
  }

  bool _isDownloadAttempt(String url) {
    // Block direct download attempts
    final downloadExtensions = [
      '.exe',
      '.msi',
      '.dmg',
      '.pkg',
      '.deb',
      '.rpm',
      '.zip',
      '.rar',
      '.7z',
      '.tar.gz',
      '.tar.bz2',
      '.apk',
      '.ipa',
      '.app',
      '.bin',
      '.run',
    ];

    return downloadExtensions.any((ext) => url.endsWith(ext));
  }

  @override
  void initState() {
    super.initState();
    _urlController = TextEditingController(text: widget.channelUrl);
  }

  @override
  void dispose() {
    _urlController.dispose();
    _isDisposing = true; // Set flag to true to stop rendering the WebView
    // Delay disposal to avoid "used after disposed" error on Windows
    // This schedules the disposal for the next event loop cycle,
    // giving the native side more time to clean up after the widget is removed.
    Future.delayed(Duration.zero, () {
      if (_webViewController != null) {
        _webViewController?.dispose();
        _webViewController = null; // Set to null to prevent further use
      }
    });
    super.dispose();
  }

  void _loadUrl() {
    final url = _urlController.text.trim();
    if (url.isNotEmpty) {
      _webViewController?.loadUrl(urlRequest: URLRequest(url: WebUri(url)));
    }
  }

  Future<void> _injectAdBlockingScript(
    InAppWebViewController controller,
  ) async {
    const script = '''
      (function() {
        console.log('Ad blocking script injected');

        // Block aggressive redirects
        const originalOpen = window.open;
        window.open = function(url, name, specs) {
          console.log('Blocked window.open attempt:', url);
          return null;
        };

        // Block location changes
        const originalAssign = window.location.assign;
        const originalReplace = window.location.replace;
        const originalReload = window.location.reload;

        window.location.assign = function(url) {
          if (url && (url.includes('opera') || url.includes('download') || url.includes('install'))) {
            console.log('Blocked location.assign:', url);
            return;
          }
          return originalAssign.call(this, url);
        };

        window.location.replace = function(url) {
          if (url && (url.includes('opera') || url.includes('download') || url.includes('install'))) {
            console.log('Blocked location.replace:', url);
            return;
          }
          return originalReplace.call(this, url);
        };

        // Block document.location changes (only if not already defined)
        try {
          let originalLocation = document.location;
          Object.defineProperty(document, 'location', {
            get: function() { return originalLocation; },
            set: function(url) {
              if (url && (url.includes('opera') || url.includes('download') || url.includes('install'))) {
                console.log('Blocked document.location change:', url);
                return;
              }
              originalLocation = url;
            },
            configurable: true
          });
        } catch (e) {
          console.log('Could not redefine document.location (already defined):', e.message);
        }

        // Block aggressive click handlers
        document.addEventListener('click', function(e) {
          const target = e.target;
          const href = target.href || target.getAttribute('href');
          if (href && (href.includes('opera') || href.includes('download') || href.includes('install'))) {
            console.log('Blocked click on suspicious link:', href);
            e.preventDefault();
            e.stopPropagation();
            return false;
          }
        }, true);

        // Block form submissions to suspicious URLs
        document.addEventListener('submit', function(e) {
          const action = e.target.action;
          if (action && (action.includes('opera') || action.includes('download') || action.includes('install'))) {
            console.log('Blocked form submission to:', action);
            e.preventDefault();
            e.stopPropagation();
            return false;
          }
        }, true);

        // Remove suspicious elements
        function removeSuspiciousElements() {
          const suspiciousSelectors = [
            'a[href*="opera"]',
            'a[href*="download"]',
            'a[href*="install"]',
            'iframe[src*="opera"]',
            'iframe[src*="download"]',
            'iframe[src*="install"]',
            '.popup',
            '.overlay',
            '.interstitial',
            '[id*="popup"]',
            '[class*="popup"]',
            '[id*="overlay"]',
            '[class*="overlay"]'
          ];

          suspiciousSelectors.forEach(selector => {
            try {
              const elements = document.querySelectorAll(selector);
              elements.forEach(el => {
                console.log('Removing suspicious element:', el);
                el.remove();
              });
            } catch (e) {
              // Ignore errors
            }
          });
        }

        // Run immediately and on DOM changes
        removeSuspiciousElements();

        // Watch for new elements
        const observer = new MutationObserver(function(mutations) {
          mutations.forEach(function(mutation) {
            if (mutation.type === 'childList') {
              removeSuspiciousElements();
            }
          });
        });

        observer.observe(document.body, {
          childList: true,
          subtree: true
        });

        console.log('Ad blocking script fully loaded');
      })();
    ''';

    try {
      await controller.evaluateJavascript(source: script);
      if (kDebugMode) {
        print('Ad blocking script injected successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error injecting ad blocking script: $e');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.channel.name),
        actions: [
          IconButton(icon: const Icon(Icons.play_arrow), onPressed: _loadUrl),
        ],
      ),
      body:
          _isDisposing // Conditionally render the WebView
              ? const SizedBox.shrink() // Render an empty box when disposing
              : InAppWebView(
                initialUrlRequest: URLRequest(url: WebUri(widget.channelUrl)),
                initialSettings: InAppWebViewSettings(
                  javaScriptEnabled: true,
                  mediaPlaybackRequiresUserGesture: false,
                  domStorageEnabled: true,
                  databaseEnabled: true,
                  // Enhanced popup and redirect blocking
                  javaScriptCanOpenWindowsAutomatically: false,
                  supportMultipleWindows: false,
                  allowsInlineMediaPlayback: true,
                  allowsBackForwardNavigationGestures: false,
                  // Block automatic downloads
                  allowsAirPlayForMediaPlayback: false,
                  // Additional security settings
                  allowFileAccessFromFileURLs: false,
                  allowUniversalAccessFromFileURLs: false,
                  // Block mixed content
                  mixedContentMode: MixedContentMode.MIXED_CONTENT_NEVER_ALLOW,
                ),
                // Intercept and block ad requests
                shouldOverrideUrlLoading: (controller, navigationAction) async {
                  final url = navigationAction.request.url.toString();
                  if (_shouldBlockUrl(url)) {
                    if (kDebugMode) {
                      print("Blocking ad request: $url");
                    }
                    return NavigationActionPolicy.CANCEL;
                  }
                  return NavigationActionPolicy.ALLOW;
                },
                // Intercept and block requests to create new windows (popups).
                onCreateWindow: (controller, createWindowRequest) async {
                  if (kDebugMode) {
                    print("Blocking popup: ${createWindowRequest.request.url}");
                  }
                  return false; // Return false to block the window from being created
                },
                onWebViewCreated: (controller) {
                  _webViewController = controller;
                },
                onLoadStart: (controller, url) {
                  if (kDebugMode) {
                    print("WebView started loading: $url");
                  }
                },
                onLoadStop: (controller, url) async {
                  if (kDebugMode) {
                    print("WebView finished loading: $url");
                  }

                  // Inject JavaScript to block aggressive redirects and downloads
                  await _injectAdBlockingScript(controller);
                },
                onReceivedError: (controller, request, error) {
                  if (kDebugMode) {
                    print(
                      "Error loading ${request.url}: ${error.description} (Code: ${error.type})",
                    );
                  }
                },
                onConsoleMessage: (controller, consoleMessage) {
                  if (kDebugMode) {
                    print("Console Message: ${consoleMessage.message}");
                  }
                },
              ),
    );
  }
}
