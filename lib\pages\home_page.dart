import 'package:flutter/material.dart';
import 'package:cat_tv/db/database_loader.dart';
import 'package:cat_tv/repositories/channel_repository.dart';
import 'package:cat_tv/repositories/region_repository.dart';
import 'package:cat_tv/repositories/country_repository.dart';
import 'package:cat_tv/repositories/language_repository.dart';
import 'package:cat_tv/repositories/category_repository.dart';
import 'package:cat_tv/models/channel.dart';
import 'package:cat_tv/models/region.dart';
import 'package:cat_tv/models/country.dart';
import 'package:cat_tv/models/language.dart';
import 'package:cat_tv/models/category.dart' as cat_model;
import 'package:sqflite/sqflite.dart';
import 'package:flutter/foundation.dart';
import 'package:cat_tv/pages/fixtures_page.dart';
import 'package:cat_tv/widgets/filter_widget.dart';
import 'package:cat_tv/controllers/filter_controller.dart' as filter_ctrl;
import 'package:cat_tv/pages/webview_page.dart';

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  int _selectedIndex = 0;
  final List<Channel> _channels = [];
  bool _isLoading = false;
  bool _hasMore = true;
  int _currentOffset = 0;
  final int _pageSize = 20;
  late ChannelRepository _repo;
  late Database _db;
  final ScrollController _scrollController = ScrollController();
  final filter_ctrl.FilterController _filterController =
      filter_ctrl.FilterController();
  List<Region> _regions = [];
  List<Country> _countries = [];
  List<Language> _languages = [];
  List<cat_model.Category> _categories = [];

  @override
  void initState() {
    super.initState();
    _initDbAndLoad();
    _scrollController.addListener(_onScroll);
    _filterController.addListener(() {
      _loadMoreChannels(reset: true);
    });
  }

  Future<void> _initDbAndLoad() async {
    setState(() => _isLoading = true);
    _db = await DatabaseLoader.openPrebuiltDatabase();
    _repo = ChannelRepository(_db);
    await _loadFilterData();
    await _loadMoreChannels(reset: true);
    setState(() => _isLoading = false);
  }

  Future<void> _loadFilterData() async {
    final regionRepo = RegionRepository(_db);
    final countryRepo = CountryRepository(_db);
    final languageRepo = LanguageRepository(_db);
    final categoryRepo = CategoryRepository(_db);
    final regions = await regionRepo.getAllRegions();
    final countries = await countryRepo.getAllCountries();
    final languages = await languageRepo.getAllLanguages();
    final categories = await categoryRepo.getAllCategories();
    setState(() {
      _regions = regions;
      _countries = countries;
      _languages = languages;
      _categories = categories.cast<cat_model.Category>();
    });
  }

  Future<void> _loadMoreChannels({bool reset = false}) async {
    if (reset) {
      setState(() {
        _channels.clear();
        _currentOffset = 0;
        _hasMore = true;
      });
    }
    setState(() => _isLoading = true);
    if (kDebugMode) {
      print(
        'Loading channels: offset=$_currentOffset, limit=$_pageSize, filter=${_filterController.filter}',
      );
    }
    try {
      final newChannels = await _repo.getChannelsPaged(
        limit: _pageSize,
        offset: _currentOffset,
        filter: _filterController.filter,
      );
      if (kDebugMode) print('Loaded ${newChannels.length} channels');
      setState(() {
        _channels.addAll(newChannels);
        _currentOffset += newChannels.length;
        _hasMore = newChannels.length == _pageSize;
        _isLoading = false;
      });
    } catch (e, st) {
      if (kDebugMode) {
        print('Error loading channels: $e');
        print(st);
      }
      setState(() => _isLoading = false);
    }
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent - 200 &&
        !_isLoading &&
        _hasMore) {
      _loadMoreChannels();
    }
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _db.close();
    super.dispose();
  }

  Widget _buildChannelList() {
    if (_channels.isEmpty && _isLoading) {
      return const Center(child: CircularProgressIndicator());
    }
    return ListView.builder(
      controller: _scrollController,
      itemCount: _channels.length + (_hasMore ? 1 : 0),
      itemBuilder: (context, index) {
        if (index == _channels.length) {
          return const Padding(
            padding: EdgeInsets.all(16.0),
            child: Center(child: CircularProgressIndicator()),
          );
        }
        final channel = _channels[index];
        return ListTile(
          leading:
              channel.logoUrl != null && channel.logoUrl!.isNotEmpty
                  ? Image.network(
                    channel.logoUrl!,
                    width: 48,
                    height: 48,
                    errorBuilder:
                        (context, error, stackTrace) =>
                            const Icon(Icons.image_not_supported, size: 48),
                  )
                  : const Icon(Icons.tv, size: 48),
          title: Text(channel.name),
          subtitle: Text(channel.countryCode),
          onTap: () async {
            if (kDebugMode) {
              print('Fetching sources for channel: ${channel.channelId}');
            }
            final sources = await _repo.getChannelSources(channel.channelId);
            if (kDebugMode) {
              print('Channel sources for ${channel.channelId}: $sources');
            }
            if (!mounted) return;
            if (sources.isNotEmpty) {
              final isExternal = await _repo.isChannelSourceExternal(
                channel.channelId,
              );
              if (!mounted) return;
              if (isExternal) {
                Navigator.push(
                  // ignore: use_build_context_synchronously
                  context,
                  MaterialPageRoute(
                    builder:
                        (_) => WebViewPage(
                          channel: channel,
                          channelUrl: sources.first,
                        ),
                  ),
                );
              } else {
                Navigator.push(
                  // ignore: use_build_context_synchronously
                  context,
                  MaterialPageRoute(
                    builder:
                        (_) => FixturesPage(
                          channel: channel,
                          channelUrl: sources.first,
                        ),
                  ),
                );
              }
            } else {
              // ignore: use_build_context_synchronously
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('No sources available for this channel'),
                ),
              );
            }
          },
        );
      },
    );
  }

  List<Widget> _getWidgetOptions() {
    return <Widget>[
      Column(
        children: [
          FilterWidget(
            controller: _filterController,
            regions: _regions,
            countries: _countries,
            languages: _languages,
            categories: _categories,
          ),
          Expanded(child: _buildChannelList()),
        ],
      ),
      const Center(child: Text('Fixtures Page')),
      const Center(child: Text('Favorite Page')),
      WebViewPage(
        channel: Channel(
          channelId: 'default',
          name: 'Default WebView',
          altNames: [],
          countryCode: '',
          isActive: true,
          isExternal: true,
        ),
        channelUrl: 'about:blank',
      ), // Default URL for WebView tab
    ];
  }

  void _onItemTapped(int index) {
    setState(() {
      _selectedIndex = index;
    });
  }

  @override
  Widget build(BuildContext context) {
    final widgetOptions = _getWidgetOptions();
    return Scaffold(
      appBar: AppBar(
        title: const Text('Live Streams'),
        backgroundColor: const Color.fromARGB(255, 3, 130, 194),
      ),
      body: widgetOptions.elementAt(_selectedIndex),
      bottomNavigationBar: BottomNavigationBar(
        items: const <BottomNavigationBarItem>[
          BottomNavigationBarItem(icon: Icon(Icons.live_tv), label: 'Live'),
          BottomNavigationBarItem(
            icon: Icon(Icons.sports_soccer),
            label: 'Fixtures',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.favorite),
            label: 'Favorite',
          ),
          BottomNavigationBarItem(icon: Icon(Icons.web), label: 'WebView'),
        ],
        currentIndex: _selectedIndex,
        selectedItemColor: Colors.teal,
        unselectedItemColor: Colors.blueGrey,
        onTap: _onItemTapped,
      ),
    );
  }
}

class ChannelPage extends StatefulWidget {
  const ChannelPage({super.key});

  @override
  State<ChannelPage> createState() => _ChannelPageState();
}

class _ChannelPageState extends State<ChannelPage> {
  final filter_ctrl.FilterController _filterController =
      filter_ctrl.FilterController();
  List<Region> _regions = [];
  List<Country> _countries = [];
  List<Language> _languages = [];
  List<cat_model.Category> _categories = [];

  @override
  void initState() {
    super.initState();
    _initFilterData();
    _filterController.addListener(() {
      debugPrint('[ChannelPage] Filter changed: ${_filterController.filter}');
      loadChannels();
    });
  }

  Future<void> _initFilterData() async {
    final db = await DatabaseLoader.openPrebuiltDatabase();
    final regionRepo = RegionRepository(db);
    final countryRepo = CountryRepository(db);
    final languageRepo = LanguageRepository(db);
    final categoryRepo = CategoryRepository(db);
    final regions = await regionRepo.getAllRegions();
    final countries = await countryRepo.getAllCountries();
    final languages = await languageRepo.getAllLanguages();
    final categories = await categoryRepo.getAllCategories();
    setState(() {
      _regions = regions;
      _countries = countries;
      _languages = languages;
      _categories = categories.cast<cat_model.Category>();
    });
  }

  void loadChannels() {
    final filter = _filterController.filter;
    debugPrint('Loading with filter: $filter');
  }

  @override
  void dispose() {
    _filterController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('Channels')),
      body: Column(
        children: [
          FilterWidget(
            controller: _filterController,
            regions: _regions,
            countries: _countries,
            languages: _languages,
            categories: _categories,
          ),
          Expanded(child: Center(child: Text('Channel list will be here'))),
        ],
      ),
    );
  }
}

class Category {
  final int id;
  final String name;
  Category({required this.id, required this.name});

  factory Category.fromMap(Map<String, dynamic> map) {
    final idValue = map['id'];
    if (idValue == null) {
      if (kDebugMode) {
        print('Warning: Category row with null id: $map');
      }
      return Category(id: -1, name: map['name'] as String? ?? 'Unknown');
    }
    return Category(
      id: idValue is int ? idValue : int.tryParse(idValue.toString()) ?? -1,
      name: map['name'] as String? ?? 'Unknown',
    );
  }
}
