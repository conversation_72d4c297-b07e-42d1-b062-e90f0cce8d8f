import 'package:flutter/material.dart';
import 'dart:async'; // Import for Timer
import '../controllers/filter_controller.dart' as filter_ctrl;
import 'package:cat_tv/models/region.dart';
import 'package:cat_tv/models/country.dart';
import 'package:cat_tv/models/language.dart';
import 'package:cat_tv/models/category.dart' as cat_model;

class FilterWidget extends StatefulWidget {
  final filter_ctrl.FilterController controller;
  final List<Region> regions;
  final List<Country> countries;
  final List<Language> languages;
  final List<cat_model.Category> categories;

  const FilterWidget({
    super.key,
    required this.controller,
    required this.regions,
    required this.countries,
    required this.languages,
    required this.categories,
  });

  @override
  State<FilterWidget> createState() => _FilterWidgetState();
}

class _FilterWidgetState extends State<FilterWidget> {
  late TextEditingController _searchController;
  Timer? _debounce;

  @override
  void initState() {
    super.initState();
    _searchController = TextEditingController(
      text: widget.controller.filter.name,
    );
    widget.controller.addListener(_onFilterChanged);
  }

  @override
  void dispose() {
    _searchController.dispose();
    _debounce?.cancel();
    widget.controller.removeListener(_onFilterChanged);
    super.dispose();
  }

  void _onFilterChanged() {
    // Update the text field if the filter name changes externally (e.g., reset button)
    if (_searchController.text != widget.controller.filter.name) {
      _searchController.text = widget.controller.filter.name ?? '';
    }
  }

  void _onSearchChanged(String value) {
    if (_debounce?.isActive ?? false) _debounce!.cancel();
    _debounce = Timer(const Duration(milliseconds: 500), () {
      final newValue = value.isEmpty ? null : value;
      widget.controller.setNameFilter(newValue);
    });
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: widget.controller,
      builder: (context, _) {
        return Padding(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          child: Card(
            elevation: 2,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
              child: Column(
                children: [
                  _SearchField(
                    controller: _searchController,
                    filterController: widget.controller,
                    onSearchChanged: _onSearchChanged,
                    onSearchSubmitted: (value) {
                      _debounce?.cancel();
                      final newValue = value.isEmpty ? null : value;
                      widget.controller.setNameFilter(newValue);
                    },
                  ),
                  const SizedBox(height: 16),
                  _FilterDropdowns(
                    filterController: widget.controller,
                    regions: widget.regions,
                    countries: widget.countries,
                    languages: widget.languages,
                    categories: widget.categories,
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}

class _SearchField extends StatelessWidget {
  final TextEditingController controller;
  final filter_ctrl.FilterController filterController;
  final ValueChanged<String> onSearchChanged;
  final ValueChanged<String> onSearchSubmitted;

  const _SearchField({
    required this.controller,
    required this.filterController,
    required this.onSearchChanged,
    required this.onSearchSubmitted,
  });

  @override
  Widget build(BuildContext context) {
    return TextField(
      controller: controller,
      decoration: InputDecoration(
        labelText: 'Search by name',
        hintText: 'Enter channel name',
        prefixIcon: const Icon(Icons.search),
        suffixIcon:
            filterController.filter.name != null &&
                    filterController.filter.name!.isNotEmpty
                ? IconButton(
                  icon: const Icon(Icons.clear),
                  onPressed: () {
                    filterController.setNameFilter(null);
                  },
                )
                : null,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide.none,
        ),
        filled: true,
        fillColor: Colors.blueGrey.shade50,
      ),
      onChanged: onSearchChanged,
      onSubmitted: onSearchSubmitted,
    );
  }
}

class _FilterDropdowns extends StatelessWidget {
  final filter_ctrl.FilterController filterController;
  final List<Region> regions;
  final List<Country> countries;
  final List<Language> languages;
  final List<cat_model.Category> categories;

  const _FilterDropdowns({
    required this.filterController,
    required this.regions,
    required this.countries,
    required this.languages,
    required this.categories,
  });

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          DropdownButton<int?>(
            value: filterController.filter.categoryId,
            hint: const Text('Category'),
            items: [
              const DropdownMenuItem(
                value: null,
                child: Text('All Categories'),
              ),
              ...categories.map(
                (cat) => DropdownMenuItem(value: cat.id, child: Text(cat.name)),
              ),
            ],
            onChanged: (val) {
              if (val == null) {
                filterController.clearFilter(category: true);
              } else {
                filterController.setFilter(categoryId: val);
              }
            },
          ),
          const SizedBox(width: 16),
          DropdownButton<String?>(
            value: filterController.filter.region,
            hint: const Text('Region'),
            items: [
              const DropdownMenuItem(value: null, child: Text('All Regions')),
              ...regions.map(
                (r) => DropdownMenuItem(value: r.code, child: Text(r.name)),
              ),
            ],
            onChanged: (val) {
              if (val == null) {
                filterController.clearFilter(region: true);
              } else {
                filterController.setFilter(
                  region: val,
                  country: null,
                ); // Reset country on region change
              }
            },
          ),
          const SizedBox(width: 16),
          DropdownButton<String?>(
            value: filterController.filter.country,
            hint: const Text('Country'),
            items: [
              const DropdownMenuItem(value: null, child: Text('All Countries')),
              ...countries.map(
                (c) => DropdownMenuItem(
                  value: c.code,
                  child: Row(
                    children: [
                      if (c.flag.isNotEmpty) Text(c.flag),
                      if (c.flag.isNotEmpty) const SizedBox(width: 4),
                      Text(c.name),
                    ],
                  ),
                ),
              ),
            ],
            onChanged: (val) {
              if (val == null) {
                filterController.clearFilter(country: true);
              } else {
                filterController.setFilter(country: val);
              }
            },
          ),
          const SizedBox(width: 16),
          DropdownButton<String?>(
            value: filterController.filter.language,
            hint: const Text('Language'),
            items: [
              const DropdownMenuItem(value: null, child: Text('All Languages')),
              ...languages.map(
                (l) => DropdownMenuItem(value: l.code, child: Text(l.name)),
              ),
            ],
            onChanged: (val) {
              if (val == null) {
                filterController.clearFilter(language: true);
              } else {
                filterController.setFilter(language: val);
              }
            },
          ),
          const SizedBox(width: 16),
          ElevatedButton.icon(
            onPressed: () {
              filterController.resetAll();
            },
            icon: const Icon(Icons.refresh),
            label: const Text('Reset'),
            style: ElevatedButton.styleFrom(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              backgroundColor: Colors.blueGrey.shade50,
              foregroundColor: Colors.blueGrey.shade900,
              elevation: 0,
            ),
          ),
        ],
      ),
    );
  }
}
