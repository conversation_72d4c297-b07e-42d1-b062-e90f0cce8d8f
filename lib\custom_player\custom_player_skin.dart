import 'package:flutter/material.dart';
import 'package:media_kit_video/media_kit_video.dart';
import 'package:flutter_hls_parser/flutter_hls_parser.dart'; // Keep if needed for Variant typeFor Random

// Define callbacks for player actions
typedef OnSeekCallback = void Function(Duration duration);
typedef OnSetVolumeCallback = void Function(double volume);
typedef OnSetRateCallback = void Function(double rate);
typedef OnSwitchQualityCallback = void Function(Variant variant);
typedef OnShowMenuCallback = void Function(BuildContext context);
typedef OnToggleVolumeSliderVisibilityCallback = void Function(bool visible);

class CustomPlayerSkin extends StatefulWidget {
  final VideoController controller;
  final bool hasSource;
  final bool showControls;
  final List<Variant> availableQualities;
  final bool isLiveStream;
  final int? bufferingPercentage;
  final String? errorMessage;
  final bool isLoading;

  // Player state data passed from parent
  final Duration currentPosition;
  final Duration totalDuration;
  final double currentVolume;
  final bool isPlaying;
  final double playbackSpeed;
  final String? currentQuality;
  final String? currentSubtitle;

  // UI state data passed from parent
  final bool isVolumeSliderVisible;
  final bool areControlsVisible; // Controls visibility managed by parent
  final bool isFullScreenMode; // New: Fullscreen mode state from parent
  final Animation<double> liveAnimation;

  // Splash effects data passed from parent
  final bool showPlayPauseSplash;
  final bool showVolumeSplash;
  final String volumeSplashText;
  final bool showCatSound;
  final String catSoundText;

  // Callbacks for user interactions
  final VoidCallback? onPlayButtonPressed;
  final VoidCallback? onTapVideo;
  final VoidCallback? onEnterControls;
  final VoidCallback? onExitControls;
  final OnSeekCallback? onSeek;
  final OnSetVolumeCallback? onSetVolume;
  final VoidCallback? onToggleMute;
  final OnSetRateCallback? onSetRate;
  final OnSwitchQualityCallback? onSwitchQuality;
  final OnShowMenuCallback? onShowSubtitlesMenu;
  final OnShowMenuCallback? onShowSettingsMenu;
  final VoidCallback? onEnterPictureInPicture;
  final VoidCallback? onStartChromecast;
  final VoidCallback? onShowCatSoundEffect;
  final OnToggleVolumeSliderVisibilityCallback? onToggleVolumeSliderVisibility;
  final VoidCallback? onToggleFullScreen; // New: Callback for fullscreen toggle

  const CustomPlayerSkin({
    super.key,
    required this.controller,
    required this.hasSource,
    required this.showControls, // This will now be `areControlsVisible` from parent
    this.availableQualities = const [],
    this.isLiveStream = true,
    this.bufferingPercentage,
    this.errorMessage,
    this.isLoading = false,
    // Player state data
    required this.currentPosition,
    required this.totalDuration,
    required this.currentVolume,
    required this.isPlaying,
    required this.playbackSpeed,
    this.currentQuality,
    this.currentSubtitle,
    // UI state data
    required this.isVolumeSliderVisible,
    required this.areControlsVisible,
    required this.isFullScreenMode, // Initialize new state
    required this.liveAnimation,
    // Splash effects data
    required this.showPlayPauseSplash,
    required this.showVolumeSplash,
    required this.volumeSplashText,
    required this.showCatSound,
    required this.catSoundText,
    // Callbacks
    this.onPlayButtonPressed,
    this.onTapVideo,
    this.onEnterControls,
    this.onExitControls,
    this.onSeek,
    this.onSetVolume,
    this.onToggleMute,
    this.onSetRate,
    this.onSwitchQuality,
    this.onShowSubtitlesMenu,
    this.onShowSettingsMenu,
    this.onEnterPictureInPicture,
    this.onStartChromecast,
    this.onShowCatSoundEffect,
    this.onToggleVolumeSliderVisibility,
    this.onToggleFullScreen, // Initialize new callback
  });

  @override
  State<CustomPlayerSkin> createState() => _CustomPlayerSkinState();
}

class _CustomPlayerSkinState extends State<CustomPlayerSkin> {
  @override
  void dispose() {
    // No need to call _exitFullScreen here, as PlayerPage manages SystemChrome
    super.dispose();
  }

  void _toggleFullScreenInternal() {
    widget.onToggleFullScreen?.call();
  }

  @override
  Widget build(BuildContext context) {
    return MediaQuery(
      data: MediaQuery.of(context).copyWith(
        // This is crucial for true fullscreen behavior
        padding: widget.isFullScreenMode ? EdgeInsets.zero : null,
        viewInsets: widget.isFullScreenMode ? EdgeInsets.zero : null,
        viewPadding: widget.isFullScreenMode ? EdgeInsets.zero : null,
      ),
      child: Material(
        // Added Material widget here
        color: Colors.transparent, // Or a suitable background color
        child: MouseRegion(
          onEnter: (_) => widget.onEnterControls?.call(),
          onHover: (_) => widget.onEnterControls?.call(),
          onExit: (_) => widget.onExitControls?.call(),
          child: GestureDetector(
            onTap: () => widget.onTapVideo?.call(),
            child: Stack(
              children: [
                SizedBox.expand(
                  child: Video(
                    controller: widget.controller,
                    controls:
                        null, // Set controls to null to remove default controls
                  ),
                ),
                // Black transparent mask
                AnimatedOpacity(
                  opacity: widget.areControlsVisible ? 1.0 : 0.0,
                  duration: const Duration(milliseconds: 300),
                  child: IgnorePointer(
                    ignoring: !widget.areControlsVisible,
                    child: Container(
                      color: Colors.black.withValues(alpha: 0.3),
                    ),
                  ),
                ),
                // Default thumbnail when no video source
                if (!widget.hasSource)
                  Container(
                    color: Colors.grey[900],
                    child: const Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.video_library_outlined,
                            size: 80,
                            color: Colors.grey,
                          ),
                          SizedBox(height: 16),
                          Text(
                            'No video loaded',
                            style: TextStyle(color: Colors.grey, fontSize: 18),
                          ),
                        ],
                      ),
                    ),
                  ),
                // Big triangle play button - shows when video is loaded but not playing AND controls are not visible
                if (widget.hasSource)
                  if (!widget.isPlaying && !widget.areControlsVisible)
                    Center(
                      child: Container(
                        width: 80,
                        height: 80,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: Colors.black.withValues(alpha: 0.6),
                          border: Border.all(
                            color: Colors.white.withValues(alpha: 0.8),
                            width: 2,
                          ),
                        ),
                        child: IconButton(
                          onPressed: () {
                            widget.onPlayButtonPressed?.call();
                          },
                          icon: const Icon(
                            Icons.play_arrow,
                            color: Colors.white,
                            size: 40.0,
                          ),
                        ),
                      ),
                    ),
                // Only show controls when video source is loaded
                if (widget.hasSource)
                  AnimatedOpacity(
                    opacity: widget.areControlsVisible ? 1.0 : 0.0,
                    duration: const Duration(milliseconds: 300),
                    child: IgnorePointer(
                      ignoring: !widget.areControlsVisible,
                      child: Align(
                        alignment: Alignment.bottomCenter,
                        child: Container(
                          height: 100,
                          color: Colors.transparent,
                          padding: const EdgeInsets.symmetric(horizontal: 16.0),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.spaceAround,
                            children: [
                              // Progress bar row - made more compact
                              Padding(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 8.0,
                                ),
                                child: Row(
                                  children: [
                                    Text(
                                      widget.currentPosition
                                          .toString()
                                          .split('.')
                                          .first,
                                      style: const TextStyle(
                                        color: Colors.white,
                                        fontSize: 12,
                                      ),
                                    ),
                                    const SizedBox(width: 8),
                                    if (widget.totalDuration.inMilliseconds > 0)
                                      Expanded(
                                        child: SliderTheme(
                                          data: SliderTheme.of(
                                            context,
                                          ).copyWith(
                                            trackHeight: 3.0,
                                            thumbShape:
                                                const RoundSliderThumbShape(
                                                  enabledThumbRadius: 6.0,
                                                ),
                                            overlayShape:
                                                const RoundSliderOverlayShape(
                                                  overlayRadius: 12.0,
                                                ),
                                          ),
                                          child: Slider(
                                            value:
                                                widget
                                                    .currentPosition
                                                    .inMilliseconds
                                                    .toDouble(),
                                            min: 0.0,
                                            max:
                                                widget
                                                    .totalDuration
                                                    .inMilliseconds
                                                    .toDouble(),
                                            onChanged: (value) {
                                              widget.onSeek?.call(
                                                Duration(
                                                  milliseconds: value.toInt(),
                                                ),
                                              );
                                            },
                                            activeColor: Colors.green,
                                            inactiveColor: Colors.grey,
                                          ),
                                        ),
                                      ),
                                    const SizedBox(width: 8),
                                    Text(
                                      widget.totalDuration
                                          .toString()
                                          .split('.')
                                          .first,
                                      style: const TextStyle(
                                        color: Colors.white,
                                        fontSize: 12,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  // Left side: Volume control and Live indicator
                                  Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      // Volume control - improved with smaller thumb
                                      MouseRegion(
                                        onEnter:
                                            (_) => widget
                                                .onToggleVolumeSliderVisibility
                                                ?.call(true),
                                        onExit:
                                            (_) => widget
                                                .onToggleVolumeSliderVisibility
                                                ?.call(false),
                                        child: Row(
                                          mainAxisSize: MainAxisSize.min,
                                          children: [
                                            IconButton(
                                              icon: Icon(
                                                widget.currentVolume == 0
                                                    ? Icons.volume_off
                                                    : Icons.volume_up,
                                                color: Colors.white,
                                                size: 20.0,
                                              ),
                                              onPressed:
                                                  () =>
                                                      widget.onToggleMute
                                                          ?.call(),
                                            ),
                                            AnimatedOpacity(
                                              opacity:
                                                  widget.isVolumeSliderVisible
                                                      ? 1.0
                                                      : 0.0,
                                              duration: const Duration(
                                                milliseconds: 300,
                                              ),
                                              child: IgnorePointer(
                                                ignoring:
                                                    !widget
                                                        .isVolumeSliderVisible,
                                                child: SizedBox(
                                                  width:
                                                      80, // Made more compact
                                                  child: SliderTheme(
                                                    data: SliderTheme.of(
                                                      context,
                                                    ).copyWith(
                                                      trackHeight: 2.0,
                                                      thumbShape:
                                                          const RoundSliderThumbShape(
                                                            enabledThumbRadius:
                                                                4.0, // Smaller thumb
                                                          ),
                                                      overlayShape:
                                                          const RoundSliderOverlayShape(
                                                            overlayRadius: 8.0,
                                                          ),
                                                    ),
                                                    child: Slider(
                                                      value:
                                                          widget.currentVolume,
                                                      min: 0.0,
                                                      max: 100.0,
                                                      onChanged:
                                                          (value) => widget
                                                              .onSetVolume
                                                              ?.call(value),
                                                      activeColor: Colors.green,
                                                      inactiveColor:
                                                          Colors.grey,
                                                    ),
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                      // Live indicator inside controls (transparent with animated red dot)
                                      if (widget.isLiveStream)
                                        Container(
                                          margin: const EdgeInsets.only(
                                            left: 16,
                                          ),
                                          child: Row(
                                            mainAxisSize: MainAxisSize.min,
                                            children: [
                                              AnimatedBuilder(
                                                animation: widget.liveAnimation,
                                                builder: (context, child) {
                                                  return Container(
                                                    width: 8,
                                                    height: 8,
                                                    decoration: BoxDecoration(
                                                      color: Colors.red
                                                          .withValues(
                                                            alpha:
                                                                widget
                                                                    .liveAnimation
                                                                    .value,
                                                          ),
                                                      shape: BoxShape.circle,
                                                    ),
                                                  );
                                                },
                                              ),
                                              const SizedBox(width: 6),
                                              const Text(
                                                'LIVE',
                                                style: TextStyle(
                                                  color: Colors.white,
                                                  fontSize: 12,
                                                  fontWeight: FontWeight.bold,
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                    ],
                                  ),
                                  // Main controls (play/pause, forward/rewind) - more compact
                                  Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      IconButton(
                                        iconSize: 20.0,
                                        icon: const Icon(
                                          Icons.replay_10,
                                          color: Colors.white,
                                        ),
                                        onPressed:
                                            () => widget.onSeek?.call(
                                              widget.currentPosition -
                                                  const Duration(seconds: 10),
                                            ),
                                      ),
                                      IconButton(
                                        iconSize: 32.0,
                                        icon: Icon(
                                          widget.isPlaying
                                              ? Icons.pause
                                              : Icons.play_arrow,
                                          color: Colors.white,
                                        ),
                                        onPressed:
                                            () => widget.onTapVideo?.call(),
                                      ),
                                      IconButton(
                                        iconSize: 20.0,
                                        icon: const Icon(
                                          Icons.forward_10,
                                          color: Colors.white,
                                        ),
                                        onPressed:
                                            () => widget.onSeek?.call(
                                              widget.currentPosition +
                                                  const Duration(seconds: 10),
                                            ),
                                      ),
                                    ],
                                  ),
                                  // Right side controls: Subtitles > Settings > PiP > Fullscreen
                                  Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      // Subtitles button
                                      Builder(
                                        builder: (buttonContext) {
                                          return IconButton(
                                            iconSize: 20.0,
                                            icon: const Icon(
                                              Icons.subtitles,
                                              color: Colors.white,
                                            ),
                                            onPressed:
                                                () => widget.onShowSubtitlesMenu
                                                    ?.call(buttonContext),
                                          );
                                        },
                                      ),
                                      // Settings button
                                      Builder(
                                        builder: (buttonContext) {
                                          return IconButton(
                                            iconSize: 20.0,
                                            icon: const Icon(
                                              Icons.settings,
                                              color: Colors.white,
                                            ),
                                            onPressed:
                                                () => widget.onShowSettingsMenu
                                                    ?.call(buttonContext),
                                          );
                                        },
                                      ),
                                      // Picture-in-Picture button
                                      IconButton(
                                        iconSize: 20.0,
                                        icon: const Icon(
                                          Icons.picture_in_picture_alt,
                                          color: Colors.white,
                                        ),
                                        onPressed:
                                            () =>
                                                widget.onEnterPictureInPicture
                                                    ?.call(),
                                      ),
                                      // Fullscreen button
                                      IconButton(
                                        iconSize: 20.0,
                                        icon: Icon(
                                          widget.isFullScreenMode
                                              ? Icons.fullscreen_exit
                                              : Icons.fullscreen,
                                          color: Colors.white,
                                        ),
                                        onPressed: _toggleFullScreenInternal,
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                // Play/Pause splash effect
                if (widget.showPlayPauseSplash)
                  Center(
                    child: Container(
                      width: 100,
                      height: 100,
                      decoration: BoxDecoration(
                        color: Colors.black.withValues(alpha: 0.6),
                        borderRadius: BorderRadius.circular(50),
                      ),
                      child: Icon(
                        widget.isPlaying ? Icons.pause : Icons.play_arrow,
                        color: Colors.white,
                        size: 50,
                      ),
                    ),
                  ),
                // Volume splash effect
                if (widget.showVolumeSplash)
                  Center(
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 20,
                        vertical: 10,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.black.withValues(alpha: 0.6),
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: Text(
                        widget.volumeSplashText,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                // Cat sound splash effect
                if (widget.showCatSound)
                  Center(
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 30,
                        vertical: 15,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.pink.withValues(alpha: 0.8),
                        borderRadius: BorderRadius.circular(25),
                        border: Border.all(
                          color: Colors.white.withValues(alpha: 0.6),
                          width: 2,
                        ),
                      ),
                      child: Text(
                        widget.catSoundText,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 28,
                          fontWeight: FontWeight.bold,
                          shadows: [
                            Shadow(
                              offset: Offset(1, 1),
                              blurRadius: 3,
                              color: Colors.black54,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                // Top left logo
                Positioned(
                  top: 16,
                  left: 16,
                  child: GestureDetector(
                    onTap: () => widget.onShowCatSoundEffect?.call(),
                    child: SizedBox(
                      width: 100,
                      height: 100,
                      child: Image.asset('assets/logo/logo.png'),
                    ),
                  ),
                ),
                // Chromecast button (top right corner)
                if (widget.hasSource)
                  Positioned(
                    top: 16,
                    right: 16,
                    child: Container(
                      width: 40,
                      height: 40,
                      decoration: BoxDecoration(
                        color: Colors.transparent, // No background
                        borderRadius: BorderRadius.circular(20),
                        border: Border.all(
                          color: Colors.white, // White outline
                          width: 1.5, // Outline thickness
                        ),
                      ),
                      child: IconButton(
                        icon: const Icon(
                          Icons.cast,
                          color: Colors.white,
                          size: 20,
                        ),
                        onPressed: () => widget.onStartChromecast?.call(),
                      ),
                    ),
                  ),
                // Loading indicator when opening a stream (only if no error message and not playing yet)
                if (widget.isLoading &&
                    widget.errorMessage == null &&
                    !widget.isPlaying)
                  Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const CircularProgressIndicator(
                          valueColor: AlwaysStoppedAnimation<Color>(
                            Colors.white,
                          ),
                          strokeWidth: 6,
                        ),
                        const SizedBox(height: 16),
                        const Text(
                          'Opening stream...',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ),
                // Buffering indicator with percentage (only if no error message and already playing)
                if (widget.isLoading &&
                    widget.bufferingPercentage != null &&
                    widget.errorMessage == null &&
                    widget.isPlaying)
                  Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        CircularProgressIndicator(
                          value: widget.bufferingPercentage! / 100,
                          valueColor: const AlwaysStoppedAnimation<Color>(
                            Colors.white,
                          ),
                          strokeWidth: 6,
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'Buffering: ${widget.bufferingPercentage}%',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ),
                // Error message display
                if (widget.errorMessage != null)
                  Center(
                    child: Container(
                      padding: const EdgeInsets.all(20),
                      decoration: BoxDecoration(
                        color: Colors.black.withValues(alpha: 0.7),
                        borderRadius: BorderRadius.circular(10),
                      ),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          const Icon(
                            Icons.error_outline,
                            color: Colors.red,
                            size: 50,
                          ),
                          const SizedBox(height: 10),
                          Text(
                            widget.errorMessage!,
                            textAlign: TextAlign.center,
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 10),
                          const Text(
                            'Please try a different stream or check your network connection.',
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              color: Colors.white70,
                              fontSize: 14,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
              ],
            ),
          ),
        ), // Closing MouseRegion
      ), // Closing Material
    );
  }
}
