import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:html/parser.dart' as html_parser;
import 'package:html/dom.dart';
import 'package:cat_tv/models/fixture.dart';

class FixturesScraper {
  static const String _baseUrl = 'https://www.livesoccertv.com';
  static const String _schedulesUrl = '$_baseUrl/schedules/';
  static const String _cacheFilePath = 'cache/fixtures_data.json';
  static const String _lastUpdateFilePath = 'cache/last_fixtures_update.txt';

  static Future<bool> shouldUpdateFixtures() async {
    try {
      final lastUpdateFile = File(_lastUpdateFilePath);
      if (!await lastUpdateFile.exists()) {
        return true; // No previous update, should update
      }

      final lastUpdateStr = await lastUpdateFile.readAsString();
      final lastUpdate = DateTime.tryParse(lastUpdateStr);
      if (lastUpdate == null) {
        return true; // Invalid date, should update
      }

      final now = DateTime.now();
      final difference = now.difference(lastUpdate);

      // Update if more than 24 hours have passed
      return difference.inHours >= 24;
    } catch (e) {
      print('Error checking last update: $e');
      return true; // Error occurred, should update
    }
  }

  static Future<List<FixtureLeague>> scrapeFixtures() async {
    int retryCount = 0;
    const maxRetries = 3;

    while (retryCount < maxRetries) {
      try {
        print(
          'Scraping fixtures from: $_schedulesUrl (attempt ${retryCount + 1})',
        );

        // Add delay between retries
        if (retryCount > 0) {
          await Future.delayed(Duration(seconds: retryCount * 2));
        }

        final response = await http.get(
          Uri.parse(_schedulesUrl),
          headers: {
            'User-Agent':
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept':
                'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1',
            'Cache-Control': 'max-age=0',
            'Referer': 'https://www.google.com/',
          },
        );

        if (response.statusCode == 403 || response.statusCode == 429) {
          retryCount++;
          if (retryCount >= maxRetries) {
            throw Exception(
              'Website is blocking requests after $maxRetries attempts. Status: ${response.statusCode}',
            );
          }
          print(
            'Request blocked (${response.statusCode}), retrying in ${retryCount * 2} seconds...',
          );
          continue;
        }

        if (response.statusCode != 200) {
          throw Exception(
            'Failed to load fixtures page: ${response.statusCode}',
          );
        }

        // Success, break out of retry loop
        final document = html_parser.parse(response.body);
        final leagues = <FixtureLeague>[];

        // Find all league sections
        final leagueSections = document.querySelectorAll('.competition');

        for (final section in leagueSections) {
          try {
            final league = _parseLeagueSection(section);
            if (league != null && league.matches.isNotEmpty) {
              leagues.add(league);
            }
          } catch (e) {
            print('Error parsing league section: $e');
            continue;
          }
        }

        print('Scraped ${leagues.length} leagues with matches');
        return leagues;
      } catch (e) {
        retryCount++;
        if (retryCount >= maxRetries) {
          print('Error scraping fixtures after $maxRetries attempts: $e');
          rethrow;
        }
        print('Error on attempt $retryCount: $e, retrying...');
      }
    }

    // This should never be reached, but just in case
    throw Exception('Failed to scrape fixtures after all retries');
  }

  static FixtureLeague? _parseLeagueSection(Element section) {
    try {
      // Get league name
      final leagueNameElement =
          section.querySelector('.competition-name') ??
          section.querySelector('h3') ??
          section.querySelector('.title');
      if (leagueNameElement == null) return null;

      final leagueName = leagueNameElement.text.trim();
      if (leagueName.isEmpty) return null;

      // Get league URL
      final leagueLink = leagueNameElement.querySelector('a');
      final leagueUrl = leagueLink?.attributes['href'] ?? '';

      // Determine flag class from league name or URL
      final flagClass = _determineFlagClass(leagueName, leagueUrl);

      // Get all matches in this league
      final matches = <FixtureMatch>[];
      final matchElements = section.querySelectorAll('.match, .fixture, tr');

      for (final matchElement in matchElements) {
        try {
          final match = _parseMatchElement(matchElement);
          if (match != null) {
            matches.add(match);
          }
        } catch (e) {
          print('Error parsing match: $e');
          continue;
        }
      }

      return FixtureLeague(
        leagueName: leagueName,
        flagClass: flagClass,
        leagueUrl: leagueUrl,
        matches: matches,
      );
    } catch (e) {
      print('Error parsing league section: $e');
      return null;
    }
  }

  static FixtureMatch? _parseMatchElement(Element element) {
    try {
      // Get match time
      final timeElement = element.querySelector('.time, .match-time, .kickoff');
      final time = timeElement?.text.trim() ?? '';

      // Get teams
      final teamElements = element.querySelectorAll('.team, .team-name');
      if (teamElements.length < 2) {
        // Try alternative selectors
        final homeTeam =
            element.querySelector('.home-team, .team-home')?.text.trim() ?? '';
        final awayTeam =
            element.querySelector('.away-team, .team-away')?.text.trim() ?? '';

        if (homeTeam.isEmpty || awayTeam.isEmpty) return null;

        final teams = '$homeTeam vs $awayTeam';
        final title = teams;

        // Get match URL
        final matchLink = element.querySelector('a');
        final matchUrl = matchLink?.attributes['href'] ?? '';

        // Get channels
        final channels = _parseChannels(element);

        // Generate match ID
        final matchId = _generateMatchId(homeTeam, awayTeam, time);

        return FixtureMatch(
          matchId: matchId,
          time: time,
          title: title,
          teams: teams,
          url: matchUrl,
          channels: channels,
        );
      }

      final homeTeam = teamElements[0].text.trim();
      final awayTeam = teamElements[1].text.trim();

      if (homeTeam.isEmpty || awayTeam.isEmpty) return null;

      // Check for score
      final scoreElement = element.querySelector('.score, .result');
      final score = scoreElement?.text.trim() ?? '';

      final teams =
          score.isNotEmpty
              ? '$homeTeam$score$awayTeam'
              : '$homeTeam vs $awayTeam';
      final title = '$homeTeam vs $awayTeam';

      // Get match URL
      final matchLink = element.querySelector('a');
      final matchUrl = matchLink?.attributes['href'] ?? '';

      // Get channels
      final channels = _parseChannels(element);

      // Generate match ID
      final matchId = _generateMatchId(homeTeam, awayTeam, time);

      return FixtureMatch(
        matchId: matchId,
        time: time,
        title: title,
        teams: teams,
        url: matchUrl,
        channels: channels,
      );
    } catch (e) {
      print('Error parsing match element: $e');
      return null;
    }
  }

  static List<String> _parseChannels(Element element) {
    final channels = <String>[];

    // Look for channel elements
    final channelElements = element.querySelectorAll(
      '.channel, .broadcaster, .tv-channel',
    );
    for (final channelElement in channelElements) {
      final channelName = channelElement.text.trim();
      if (channelName.isNotEmpty) {
        channels.add(channelName);
      }
    }

    // If no channels found, look for alternative selectors
    if (channels.isEmpty) {
      final channelText =
          element.querySelector('.channels, .broadcasters')?.text.trim();
      if (channelText != null && channelText.isNotEmpty) {
        // Split by common separators
        final splitChannels = channelText.split(RegExp(r'[,;|]'));
        for (final channel in splitChannels) {
          final trimmed = channel.trim();
          if (trimmed.isNotEmpty) {
            channels.add(trimmed);
          }
        }
      }
    }

    return channels;
  }

  static String _determineFlagClass(String leagueName, String leagueUrl) {
    final lowerName = leagueName.toLowerCase();
    final lowerUrl = leagueUrl.toLowerCase();

    // Map common league names to flag classes
    final flagMappings = {
      'premier league': 'england',
      'championship': 'england',
      'la liga': 'spain',
      'serie a': 'italy',
      'bundesliga': 'germany',
      'ligue 1': 'france',
      'eredivisie': 'netherlands',
      'primeira liga': 'portugal',
      'champions league': 'europe',
      'europa league': 'europe',
      'uefa': 'europe',
      'world cup': 'fifa',
      'euro': 'europe',
      'copa america': 'south-america',
      'africa cup': 'africa',
      'asian cup': 'asia',
    };

    for (final entry in flagMappings.entries) {
      if (lowerName.contains(entry.key) || lowerUrl.contains(entry.key)) {
        return entry.value;
      }
    }

    // Try to extract country from URL or name
    if (lowerUrl.contains('/england/')) return 'england';
    if (lowerUrl.contains('/spain/')) return 'spain';
    if (lowerUrl.contains('/italy/')) return 'italy';
    if (lowerUrl.contains('/germany/')) return 'germany';
    if (lowerUrl.contains('/france/')) return 'france';
    if (lowerUrl.contains('/netherlands/')) return 'netherlands';
    if (lowerUrl.contains('/portugal/')) return 'portugal';
    if (lowerUrl.contains('/brazil/')) return 'brazil';
    if (lowerUrl.contains('/argentina/')) return 'argentina';
    if (lowerUrl.contains('/international/')) return 'europe';

    return 'world'; // Default flag
  }

  static String _generateMatchId(
    String homeTeam,
    String awayTeam,
    String time,
  ) {
    final combined = '$homeTeam-$awayTeam-$time';
    return combined.hashCode.abs().toString();
  }

  static Future<void> saveFixturesToCache(List<FixtureLeague> leagues) async {
    try {
      // Ensure cache directory exists
      final cacheDir = Directory('cache');
      if (!await cacheDir.exists()) {
        await cacheDir.create(recursive: true);
      }

      // Convert to JSON
      final jsonData =
          leagues
              .map(
                (league) => {
                  'league_name': league.leagueName,
                  'flag_class': league.flagClass,
                  'league_url': league.leagueUrl,
                  'matches':
                      league.matches
                          .map(
                            (match) => {
                              'match_id': match.matchId,
                              'time': match.time,
                              'title': match.title,
                              'teams': match.teams,
                              'url': match.url,
                              'channels': match.channels,
                            },
                          )
                          .toList(),
                },
              )
              .toList();

      // Save to file
      final file = File(_cacheFilePath);
      await file.writeAsString(json.encode(jsonData));

      // Update last update timestamp
      final lastUpdateFile = File(_lastUpdateFilePath);
      await lastUpdateFile.writeAsString(DateTime.now().toIso8601String());

      print('Fixtures saved to cache: ${leagues.length} leagues');
    } catch (e) {
      print('Error saving fixtures to cache: $e');
      rethrow;
    }
  }

  static Future<List<FixtureLeague>> updateFixturesIfNeeded() async {
    try {
      if (await shouldUpdateFixtures()) {
        print('Updating fixtures...');
        try {
          final leagues = await scrapeFixtures();
          await saveFixturesToCache(leagues);
          return leagues;
        } catch (e) {
          print('Scraping failed, creating sample data: $e');
          // If scraping fails, create some sample data for testing
          final sampleLeagues = _createSampleFixtures();
          await saveFixturesToCache(sampleLeagues);
          return sampleLeagues;
        }
      } else {
        print('Fixtures are up to date, loading from cache...');
        return await loadFixturesFromCache();
      }
    } catch (e) {
      print('Error updating fixtures, trying to load from cache: $e');
      final cachedLeagues = await loadFixturesFromCache();
      if (cachedLeagues.isEmpty) {
        // If cache is also empty, return sample data
        return _createSampleFixtures();
      }
      return cachedLeagues;
    }
  }

  static List<FixtureLeague> _createSampleFixtures() {
    return [
      FixtureLeague(
        leagueName: 'Premier League',
        flagClass: 'england',
        leagueUrl: '/competitions/england/premier-league/',
        matches: [
          FixtureMatch(
            matchId: '1001',
            time: '15:00',
            title: 'Manchester United vs Liverpool',
            teams: 'Manchester United vs Liverpool',
            url: '/match/manchester-united-vs-liverpool/test1',
            channels: ['Sky Sports', 'NBC Sports', 'DAZN'],
          ),
          FixtureMatch(
            matchId: '1002',
            time: '17:30',
            title: 'Arsenal vs Chelsea',
            teams: 'Arsenal2 - 1Chelsea',
            url: '/match/arsenal-vs-chelsea/test2',
            channels: ['BT Sport', 'ESPN', 'beIN Sports'],
          ),
        ],
      ),
      FixtureLeague(
        leagueName: 'La Liga',
        flagClass: 'spain',
        leagueUrl: '/competitions/spain/la-liga/',
        matches: [
          FixtureMatch(
            matchId: '2001',
            time: '20:00',
            title: 'Real Madrid vs Barcelona',
            teams: 'Real Madrid vs Barcelona',
            url: '/match/real-madrid-vs-barcelona/test3',
            channels: ['ESPN+', 'Movistar+', 'beIN Sports'],
          ),
        ],
      ),
    ];
  }

  static Future<List<FixtureLeague>> loadFixturesFromCache() async {
    try {
      final file = File(_cacheFilePath);
      if (!await file.exists()) {
        return [];
      }

      final jsonString = await file.readAsString();
      final List<dynamic> jsonData = json.decode(jsonString);

      return jsonData.map((league) => FixtureLeague.fromJson(league)).toList();
    } catch (e) {
      print('Error loading fixtures from cache: $e');
      return [];
    }
  }
}
